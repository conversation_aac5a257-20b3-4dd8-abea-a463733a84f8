name: CI/CD Pipeline

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ main ]

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      python-version: ${{ steps.setup-python.outputs.python-version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        id: setup-python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

  test:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run tests
        run: pytest -q

  docker-build:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Fly.io registry
        run: |
          echo ${{ secrets.FLY_API_TOKEN }} | docker login registry.fly.io -u x --password-stdin

      - name: Build and push Docker image
        run: |
          docker build \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            --cache-from registry.fly.io/ailex-auth:latest \
            -t registry.fly.io/ailex-auth:${{ github.sha }} \
            -t registry.fly.io/ailex-auth:latest \
            .
          docker push registry.fly.io/ailex-auth:${{ github.sha }}
          docker push registry.fly.io/ailex-auth:latest

  deploy:
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Fly CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy to Fly.io
        run: |
          flyctl deploy \
            --image registry.fly.io/ailex-auth:${{ github.sha }} \
            --remote-only \
            --app ailex-auth
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
